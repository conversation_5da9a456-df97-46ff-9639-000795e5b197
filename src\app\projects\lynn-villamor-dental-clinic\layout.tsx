import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Lynn Villamor Dental Clinic - Healthcare Platform Case Study",
  description: "Comprehensive healthcare platform for dental clinic management. Features advanced appointment scheduling, digital prescriptions, intraoral charting, and HIPAA-compliant patient records. Live demo available at dental-clinic-webapp.vercel.app.",
  openGraph: {
    title: "Lynn Villamor Dental Clinic - Healthcare Platform by <PERSON>",
    description: "Live healthcare platform with advanced appointment scheduling, digital prescriptions, and comprehensive patient management. Built with Next.js 14 and deployed on Vercel.",
    url: "https://hayagerwin.github.io/projects/lynn-villamor-dental-clinic",
    images: [
      {
        url: "/assets/images/lynn-villamor-dental-clinic/cover.png",
        width: 1200,
        height: 630,
        alt: "Lynn Villamor Dental Clinic Healthcare Platform - Live Demo Available",
      },
    ],
  },
};

export default function DentalClinicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
