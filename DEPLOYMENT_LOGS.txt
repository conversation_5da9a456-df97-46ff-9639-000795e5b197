[17:34:25.642] Running build in Washington, D.C., USA (East) – iad1
[17:34:25.642] Build machine configuration: 2 cores, 8 GB
[17:34:25.659] Cloning github.com/hayagerwin/web-portfolio (Branch: main, Commit: 57aab22)
[17:34:27.103] Cloning completed: 1.444s
[17:34:29.205] Restored build cache from previous deployment (HU6bvaafPe7ojw32anrixQYvVT5X)
[17:34:31.046] Running "vercel build"
[17:34:31.573] Vercel CLI 44.4.3
[17:34:31.874] Installing dependencies...
[17:34:33.108] 
[17:34:33.109] up to date in 963ms
[17:34:33.109] 
[17:34:33.109] 143 packages are looking for funding
[17:34:33.109]   run `npm fund` for details
[17:34:33.137] Detected Next.js version: 15.3.4
[17:34:33.138] Running "npm run build"
[17:34:33.246] 
[17:34:33.246] > web-portfolio@0.1.0 build
[17:34:33.247] > next build
[17:34:33.247] 
[17:34:33.969]    ▲ Next.js 15.3.4
[17:34:33.970] 
[17:34:34.146]    Creating an optimized production build ...
[17:34:58.730]  ✓ Compiled successfully in 21.0s
[17:34:58.735]    Linting and checking validity of types ...
[17:35:05.911] Failed to compile.
[17:35:05.911] 
[17:35:05.912] ./src/app/api/hello/route.ts:4:27
[17:35:05.912] Type error: 'request' is declared but its value is never read.
[17:35:05.912] 
[17:35:05.912] [0m [90m 2 |[39m [36mimport[39m { [33mNextRequest[39m[33m,[39m [33mNextResponse[39m } [36mfrom[39m [32m"next/server"[39m[33m;[39m[0m
[17:35:05.912] [0m [90m 3 |[39m[0m
[17:35:05.912] [0m[31m[1m>[22m[39m[90m 4 |[39m [36mexport[39m [36masync[39m [36mfunction[39m [33mGET[39m(request[33m:[39m [33mNextRequest[39m) {[0m
[17:35:05.913] [0m [90m   |[39m                           [31m[1m^[22m[39m[0m
[17:35:05.913] [0m [90m 5 |[39m   [36mreturn[39m [33mNextResponse[39m[33m.[39mjson({ name[33m:[39m [32m"SEN Tenz"[39m })[33m;[39m[0m
[17:35:05.913] [0m [90m 6 |[39m }[0m
[17:35:05.913] [0m [90m 7 |[39m[0m
[17:35:05.932] Next.js build worker exited with code: 1 and signal: null
[17:35:05.951] Error: Command "npm run build" exited with 1
[17:35:06.155] 
[17:35:09.152] Exiting build container