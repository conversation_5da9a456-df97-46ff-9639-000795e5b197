"use client";
import Image from "next/image";
import { useState } from "react";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  placeholder?: "blur" | "empty";
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  quality?: number;
}

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = "",
  priority = false,
  placeholder = "empty",
  blurDataURL,
  sizes,
  fill = false,
  quality = 75,
  ...props
}: OptimizedImageProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div
        className={`bg-dark-secondary/50 flex items-center justify-center ${className}`}
        style={{ width: width || 'auto', height: height || 'auto' }}
        role="img"
        aria-label={`Failed to load image: ${alt}`}
      >
        <span className="text-text-muted text-sm">Image unavailable</span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div
          className="absolute inset-0 bg-dark-secondary/50 animate-pulse rounded-lg"
          aria-hidden="true"
        />
      )}
      <Image
        src={src}
        alt={alt}
        {...(fill ? { fill: true } : { width: width!, height: height! })}
        priority={priority}
        placeholder={placeholder}
        {...(blurDataURL && { blurDataURL })}
        sizes={sizes}
        quality={quality}
        onLoad={handleLoad}
        onError={handleError}
        className={`transition-opacity duration-300 ${
          isLoading ? "opacity-0" : "opacity-100"
        } ${className}`}
        {...props}
      />
    </div>
  );
};

export default OptimizedImage;