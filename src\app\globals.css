@tailwind base;
@tailwind components;
@tailwind utilities;

.translate-z-0 {
  transform: translateZ(0px);
}
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets
  .swiper-pagination-bullet {
  background-color: #05ffc1;
}

.swiper-button-next:after,
.swiper-button-prev:after,
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  color: #05ffc1;
}

/* Prevent text selection for the entire document */
html,
body {
  user-select: none;
  -webkit-user-select: none; /* For Safari */
  -moz-user-select: none; /* For Firefox */
  -ms-user-select: none; /* For Internet Explorer */
}

/* Allow text selection inside specific elements */
.selectable {
  user-select: auto;
  -webkit-user-select: auto; /* For Safari */
  -moz-user-select: auto; /* For Firefox */
  -ms-user-select: auto; /* For Internet Explorer */
}

@layer base {
  body {
    @apply overflow-hidden bg-dark leading-relaxed font-inter text-text-primary;
  }
  .page {
    @apply scrollbar-hidden h-screen w-full overflow-hidden overflow-y-scroll;
  }

  /* Optimized Typography System - Reduced Sizes */
  .h1 {
    @apply mb-4 text-2xl font-bold leading-tight tracking-tight xs:text-3xl sm:text-4xl md:text-5xl md:leading-[1.1] lg:text-6xl xl:mb-6;
    background: linear-gradient(135deg, #FFFFFF 0%, #05FFC1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .h2 {
    @apply mb-3 text-xl font-bold leading-tight tracking-tight xs:text-2xl sm:text-3xl md:text-4xl md:leading-[1.2] lg:text-5xl xl:mb-4;
    background: linear-gradient(135deg, #FFFFFF 0%, #05FFC1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  .h3 {
    @apply mb-3 text-lg font-semibold leading-tight tracking-tight xs:text-xl sm:text-2xl md:text-3xl md:leading-[1.3] xl:mb-4;
    color: #FFFFFF;
  }
  .h4 {
    @apply mb-2 text-base font-semibold leading-tight tracking-tight xs:text-lg sm:text-xl md:text-2xl md:leading-[1.3] xl:mb-3;
    color: #FFFFFF;
  }
  .h5 {
    @apply mb-2 text-sm font-semibold leading-tight tracking-tight xs:text-base sm:text-lg md:text-xl xl:mb-3;
    color: #FFFFFF;
  }
  .h6 {
    @apply mb-1 text-xs font-bold leading-tight tracking-tight xs:text-sm md:text-base;
    color: #05FFC1;
  }

  /* Optimized Text Styles - Reduced Sizes */
  p {
    @apply font-normal leading-relaxed text-text-secondary text-xs xs:text-sm sm:text-base;
  }
  .text-large {
    @apply text-sm leading-relaxed text-text-secondary xs:text-base md:text-lg lg:text-xl;
  }
  .text-small {
    @apply text-xs leading-relaxed text-text-muted;
  }

  /* CSS-Only Animations with Reduced Motion Support */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fadeInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(5, 255, 193, 0.3);
    }
    50% {
      box-shadow: 0 0 30px rgba(5, 255, 193, 0.5);
    }
  }

  /* Animation Classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
  }

  .animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
  }

  .animate-glow-pulse {
    animation: glow 2s ease-in-out infinite;
  }

  /* Staggered Animation Delays */
  .animate-delay-100 { animation-delay: 0.1s; }
  .animate-delay-200 { animation-delay: 0.2s; }
  .animate-delay-300 { animation-delay: 0.3s; }
  .animate-delay-400 { animation-delay: 0.4s; }
  .animate-delay-500 { animation-delay: 0.5s; }

  /* Respect Reduced Motion Preference */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in-up,
    .animate-fade-in-left,
    .animate-fade-in-right,
    .animate-scale-in,
    .animate-shimmer,
    .animate-glow-pulse {
      animation: none;
      opacity: 1;
      transform: none;
    }

    /* Disable all transitions and animations */
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Interactive Elements - Mobile Optimized */
  .btn {
    @apply h-10 px-4 rounded-lg font-medium transition-all duration-300 ease-in-out text-sm xs:h-12 xs:px-6 xs:text-base;
    /* Larger touch targets for mobile */
    min-height: 44px;
  }
  .btn-primary {
    @apply bg-accent text-dark hover:bg-accent/90 hover:shadow-glow active:scale-95 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:ring-offset-2 focus:ring-offset-dark;
  }
  .btn-secondary {
    @apply bg-transparent border-2 border-accent text-accent hover:bg-accent hover:text-dark hover:shadow-glow active:scale-95 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:ring-offset-2 focus:ring-offset-dark;
  }
  .btn-ghost {
    @apply bg-transparent text-text-primary hover:bg-white/10 active:scale-95 focus:outline-none focus:ring-2 focus:ring-white/20 focus:ring-offset-2 focus:ring-offset-dark;
  }

  /* Consistent button sizes */
  .btn-sm {
    @apply h-8 px-3 text-xs xs:h-10 xs:px-4 xs:text-sm;
    min-height: 32px;
  }
  .btn-lg {
    @apply h-12 px-6 text-base xs:h-14 xs:px-8 xs:text-lg;
    min-height: 48px;
  }

  /* Special button variants */
  .btn-cta {
    @apply btn-primary relative overflow-hidden;
  }
  .btn-cta::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-accent to-secondary opacity-0 transition-opacity duration-300;
  }
  .btn-cta:hover::before {
    @apply opacity-20;
  }

  /* Form Elements */
  .input {
    @apply h-12 w-full rounded-lg px-4 bg-dark-secondary border border-white/20 text-text-primary placeholder:text-text-muted focus:border-accent focus:ring-1 focus:ring-accent/50 transition-all duration-300;
  }
  .textarea {
    @apply min-h-[120px] w-full resize-none rounded-lg p-4 bg-dark-secondary border border-white/20 text-text-primary placeholder:text-text-muted focus:border-accent focus:ring-1 focus:ring-accent/50 transition-all duration-300;
  }

  /* Card Styles */
  .card {
    @apply bg-dark-secondary/50 backdrop-blur-sm border border-white/10 rounded-2xl p-6 transition-all duration-300 hover:border-accent/30 hover:shadow-card-hover;
  }
  .card-glow {
    @apply card hover:shadow-glow;
  }

  /* Grid System Utilities */
  .grid-center {
    @apply grid place-items-center;
  }
  .grid-start {
    @apply grid place-items-start;
  }
  .grid-end {
    @apply grid place-items-end;
  }

  /* Text Alignment Utilities */
  .text-balance {
    text-wrap: balance;
  }
  .text-pretty {
    text-wrap: pretty;
  }

  /* Consistent spacing system */
  .section-padding {
    @apply px-4 py-8 sm:px-6 sm:py-12 lg:px-8 lg:py-16;
  }
  .content-width {
    @apply max-w-7xl mx-auto;
  }
  .content-width-narrow {
    @apply max-w-4xl mx-auto;
  }

  /* Enhanced Responsive Utilities */
  .responsive-grid {
    @apply grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
  .responsive-flex {
    @apply flex flex-col gap-4 sm:flex-row sm:items-center;
  }
  .responsive-text {
    @apply text-sm sm:text-base lg:text-lg;
  }
  .responsive-heading {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }

  /* Mobile-first touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Responsive containers */
  .container-mobile {
    @apply px-4 mx-auto max-w-sm;
  }
  .container-tablet {
    @apply px-6 mx-auto max-w-4xl;
  }
  .container-desktop {
    @apply px-8 mx-auto max-w-7xl;
  }

  /* Viewport-specific utilities */
  .mobile-only {
    @apply block sm:hidden;
  }
  .tablet-only {
    @apply hidden sm:block lg:hidden;
  }
  .desktop-only {
    @apply hidden lg:block;
  }
  .mobile-tablet {
    @apply block lg:hidden;
  }
  .tablet-desktop {
    @apply hidden sm:block;
  }

  /* Background Gradients */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #29335C 0%, #1A1A2E 100%);
  }
  .bg-gradient-accent {
    background: linear-gradient(135deg, #05FFC1 0%, #4ECDC4 100%);
  }

  /* Legacy slider backgrounds - enhanced */
  .bg-slider1 {
    @apply bg-gradient-to-br from-primary to-dark-secondary;
  }
  .bg-slider2 {
    @apply bg-gradient-to-br from-dark-secondary to-primary;
  }
  .bg-slider3 {
    @apply bg-gradient-to-br from-dark to-dark-secondary;
  }
}

/* Hide scrollbar for specific element */
.scrollbar-hidden::-webkit-scrollbar {
  width: 0;
  height: 0;
}

/* Accessibility Improvements */
@layer utilities {
  /* Enhanced Focus styles for better accessibility */
  .focus-visible:focus-visible {
    @apply outline-2 outline-offset-2 outline-accent;
  }

  /* Focus styles for interactive elements */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  [tabindex]:focus-visible {
    @apply outline-2 outline-offset-2 outline-accent;
    outline-style: solid;
  }

  /* Enhanced focus for cards and interactive containers */
  .card:focus-visible,
  .btn:focus-visible {
    @apply outline-2 outline-offset-2 outline-accent ring-2 ring-accent/30;
  }

  /* Skip link styles */
  .skip-link {
    @apply absolute -top-10 left-4 z-50 bg-accent text-dark px-4 py-2 rounded-md font-medium;
    @apply focus:top-4 transition-all duration-200;
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Screen reader content that becomes visible on focus */
  .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* ARIA live regions */
  .live-region {
    @apply sr-only;
  }

  /* Keyboard navigation helpers */
  .keyboard-only {
    @apply sr-only focus:not-sr-only focus:absolute focus:z-50;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .h1, .h2, .h3, .h4, .h5, .h6 {
      color: #FFFFFF !important;
    }
    .card {
      border-color: #FFFFFF !important;
    }
    .btn {
      border: 2px solid #FFFFFF !important;
    }
    .focus-visible:focus-visible,
    button:focus-visible,
    a:focus-visible {
      outline: 3px solid #FFFFFF !important;
      outline-offset: 2px !important;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Mobile-specific optimizations */
  @media (max-width: 768px) {
    .mobile-center {
      text-align: center !important;
    }
    .mobile-full-width {
      width: 100% !important;
    }
    .mobile-stack {
      flex-direction: column !important;
    }
    .mobile-hide {
      display: none !important;
    }
    .mobile-show {
      display: block !important;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .hover-effects {
      /* Disable hover effects on touch devices */
    }
    .btn, .card, a {
      /* Larger touch targets */
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    body {
      background: white !important;
      color: black !important;
    }
  }
}
