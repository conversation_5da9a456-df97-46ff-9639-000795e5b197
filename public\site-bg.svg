<svg width="1435" height="877" viewBox="0 0 1435 877" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_157_2)">
<rect width="1435" height="877" fill="#29335C"/>
<g filter="url(#filter0_f_157_2)">
<circle cx="172" cy="153" r="178" fill="#49537C"/>
</g>
<g filter="url(#filter1_f_157_2)">
<circle cx="1299" cy="142" r="178" fill="#49537C"/>
</g>
<g filter="url(#filter2_f_157_2)">
<circle cx="1082" cy="460" r="217" fill="#49537C"/>
</g>
</g>
<defs>
<filter id="filter0_f_157_2" x="-356" y="-375" width="1056" height="1056" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_157_2"/>
</filter>
<filter id="filter1_f_157_2" x="771" y="-386" width="1056" height="1056" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_157_2"/>
</filter>
<filter id="filter2_f_157_2" x="515" y="-107" width="1134" height="1134" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_157_2"/>
</filter>
<clipPath id="clip0_157_2">
<rect width="1435" height="877" fill="white"/>
</clipPath>
</defs>
</svg>
