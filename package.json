{"name": "web-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true next build", "preview": "next build && next start"}, "dependencies": {"@tailwindcss/line-clamp": "^0.4.4", "clsx": "^2.1.1", "framer-motion": "^11.0.18", "next": "^15.3.4", "react": "^18", "react-countup": "^6.5.2", "react-dom": "^18", "react-icons": "^5.0.1", "tailwind-merge": "^3.3.1", "tailwind-scrollbar": "^3.1.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.4", "eslint-config-prettier": "^9.1.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "tailwindcss": "^3.3.0", "typescript": "^5"}}